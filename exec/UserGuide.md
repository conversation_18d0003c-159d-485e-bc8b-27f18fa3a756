# 差分升级工具用户指南

## 概述

差分升级工具是一套用于生成和应用软件版本差分补丁的工具集，包含两个核心脚本：
- **`generate_patch.py`** - 补丁生成工具
- **`apply_patch.sh`** - 补丁应用工具

该工具支持文件增删改、权限修改、目录操作、符号链接处理等复杂场景，并提供完整的回滚机制。

## 工具特性

### 🔧 核心功能
- ✅ **文件操作**：支持文件的增加、删除、修改
- ✅ **权限管理**：精确处理文件和目录权限变更
- ✅ **目录操作**：支持目录的创建、删除、权限修改
- ✅ **符号链接**：完整支持软链接的创建、修改、删除
- ✅ **复杂场景**：软链接变目录、目录变软链接
- ✅ **依赖处理**：智能处理符号链接依赖关系
- ✅ **安全机制**：升级前后校验、自动备份、失败回滚

### 🚀 高级特性
- **智能差分**：只传输变更的文件内容，节省空间和时间
- **完整性校验**：MD5校验确保文件完整性
- **原子操作**：升级过程中断不会导致系统损坏
- **详细日志**：完整记录升级过程，便于问题排查

## 安装和准备

### 文件权限
```bash
chmod +x generate_patch.py
chmod +x apply_patch.sh
```

## 使用方法

### 1. 生成差分补丁

#### 基本语法
```bash
./generate_patch.py <旧版本目录> <新版本目录> <输出补丁目录>
```

#### 参数说明
- **旧版本目录**：当前版本的完整目录
- **新版本目录**：目标版本的完整目录  
- **输出补丁目录**：生成的补丁包存放位置

#### 使用示例
```bash
# 基本使用
./generate_patch.py /path/to/old_version /path/to/new_version /path/to/patch_output

# 实际例子
./generate_patch.py app_v1.0 app_v1.1 patch_v1.0_to_v1.1
```

#### 输出结构
```
patch_output/
├── manifest.json          # 升级清单文件
├── patches/               # 差分文件目录
│   ├── abc123.data       # 文件差分数据
│   ├── def456.data       # 文件差分数据
│   └── ...
└── symlinks.tmp          # 符号链接临时文件
```

### 2. 应用差分补丁

#### 基本语法
```bash
./apply_patch.sh <应用目录> <补丁目录> [备份目录] [日志文件]
```

#### 参数说明
- **应用目录**：需要升级的目录（必需）
- **补丁目录**：补丁包所在目录（必需）
- **备份目录**：备份存放位置（可选，默认：`/tmp/patch_backup/app_backup_YYYYMMDDHHMMSS`）
- **日志文件**：日志文件路径（可选，默认：`/tmp/patch_log/patch_YYYYMMDDHHMMSS.log`）

#### 使用示例
```bash
# 基本使用
./apply_patch.sh /path/to/app /path/to/patch

# 指定备份目录
./apply_patch.sh /path/to/app /path/to/patch /path/to/backup

# 完整参数
./apply_patch.sh /path/to/app /path/to/patch /path/to/backup /path/to/logfile.log
```

## 工作流程

### 补丁生成流程
1. **目录扫描**：递归扫描新旧版本目录
2. **差异分析**：对比文件内容、权限、类型变化
3. **依赖分析**：分析符号链接依赖关系
4. **补丁生成**：生成差分数据和升级清单
5. **完整性验证**：生成MD5校验信息

### 补丁应用流程
1. **升级前校验**：验证当前版本与补丁匹配
2. **环境准备**：创建备份和日志目录
3. **数据备份**：完整备份当前版本
4. **补丁应用**：按顺序应用所有变更
5. **升级后校验**：验证升级结果正确性
6. **清理完成**：清理临时文件，记录日志

## 高级功能

### 符号链接处理

工具支持复杂的符号链接场景：

#### 软链接变目录
```bash
# 旧版本：link_dir -> external_dir
# 新版本：link_dir/ (真实目录)
```

#### 目录变软链接  
```bash
# 旧版本：real_dir/ (真实目录)
# 新版本：real_dir -> target_dir
```

#### 复杂依赖链
```bash
# 支持符号链接链：link1 -> link2 -> link3 -> target
# 智能排序，确保正确的创建顺序
```

### 回滚机制

当升级失败时，工具会自动回滚：

1. **升级前校验失败**：直接退出，不做任何修改
2. **升级过程失败**：自动恢复备份，回滚到原始状态
3. **升级后校验失败**：自动恢复备份，确保系统完整性

### 日志和监控

#### 日志级别
- **INFO**：正常操作信息
- **WARNING**：警告信息（不影响升级）
- **ERROR**：错误信息（导致升级失败）

#### 日志内容
- 升级开始和结束时间
- 每个文件的操作记录
- 权限变更记录
- 符号链接操作记录
- 错误和警告信息

## 故障排除

### 常见问题

#### 1. 权限不足
```bash
# 错误信息
Permission denied: /path/to/file

# 解决方案
sudo ./apply_patch.sh /path/to/app /path/to/patch
```

#### 2. 磁盘空间不足
```bash
# 错误信息
No space left on device

# 解决方案
# 清理磁盘空间或指定其他备份目录
./apply_patch.sh /path/to/app /path/to/patch /other/backup/path
```

#### 3. 符号链接目标不存在
```bash
# 工具会自动处理符号链接依赖关系
# 如果仍有问题，检查符号链接目标是否正确
```

#### 4. 升级中断
```bash
# 工具会自动回滚，如需手动恢复：
cp -rp /tmp/patch_backup/app_backup_* /path/to/app/
```

### 调试技巧

#### 查看详细日志
```bash
tail -f /tmp/patch_log/patch_*.log
```

#### 验证补丁内容
```bash
# 查看升级清单
cat patch_dir/manifest.json | python3 -m json.tool

# 检查差分文件
ls -la patch_dir/patches/
```

#### 手动验证
```bash
# 对比目录差异
diff -r --no-dereference old_dir new_dir

# 检查权限
find dir -exec stat -c "%n %a" {} \;
```

## 最佳实践

### 1. 升级前准备
- 确保有足够的磁盘空间（至少2倍应用大小）
- 停止相关服务进程
- 创建完整的系统备份

### 2. 测试流程
- 在测试环境先验证补丁
- 使用测试工具验证功能完整性
- 确认回滚流程正常工作

### 3. 生产部署
- 选择业务低峰期进行升级
- 准备回滚预案
- 监控升级过程和系统状态

### 4. 版本管理
- 为每个版本创建独立的补丁包
- 保留历史版本的补丁包
- 建立版本升级路径图

## 性能优化

### 大文件处理
- 工具自动优化大文件的差分算法
- 支持二进制文件的高效处理
- 内存使用优化，支持GB级文件

### 网络传输
- 补丁包通常比完整包小90%以上
- 支持增量传输和断点续传
- 可配合压缩工具进一步减小体积

## 安全考虑

### 文件完整性
- 所有文件都有MD5校验
- 升级前后完整性验证
- 防止文件损坏和篡改

### 权限安全
- 精确保持文件权限
- 不会意外提升权限
- 支持特殊权限位处理

### 回滚安全
- 完整备份机制
- 原子操作保证
- 失败自动恢复

## 示例场景

### 场景1：应用版本升级
```bash
# 1. 准备版本目录
ls -la
# app_v1.0/  app_v1.1/

# 2. 生成补丁
./generate_patch.py app_v1.0 app_v1.1 patch_v1.0_to_v1.1

# 3. 应用补丁
./apply_patch.sh /opt/myapp patch_v1.0_to_v1.1

# 4. 验证结果
echo "升级完成，版本：$(cat /opt/myapp/version.txt)"
```

### 场景2：配置文件更新
```bash
# 只更新配置文件的差分补丁
./generate_patch.py old_config new_config config_patch
./apply_patch.sh /etc/myapp config_patch
```

### 场景3：库文件升级
```bash
# 处理复杂的库文件符号链接
# 旧版本：libtest.so -> libtest.so.1 -> libtest.so.1.2.3
# 新版本：libtest.so -> libtest.so.1 -> libtest.so.1.2.4
./generate_patch.py old_libs new_libs lib_patch
./apply_patch.sh /usr/local/lib lib_patch
```


## 版本历史

### v2.0
- ✨ 新增智能符号链接依赖处理
- ✨ 新增软链接变目录支持
- ✨ 新增目录变软链接支持
- 🐛 修复回滚时目录嵌套问题
- 🐛 修复符号链接目标不存在警告
- ⚡ 优化大文件处理性能

### v1.0
- 🎉 初始版本发布
- ✨ 基础文件增删改功能
- ✨ 权限管理功能
- ✨ 基础符号链接支持
- ✨ 备份和回滚机制


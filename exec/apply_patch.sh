#!/bin/bash
set -eo pipefail

# ==================== 配置变量 ====================
BSPATCH_PATH="./bspatch"

# ==================== 使用方法 ====================
# 示例: ./apply_patch.sh APP_DIR PATCH_DIR
# 参数说明:
#   APP_DIR    : 旧版本目录 (必须)
#   PATCH_DIR  : 补丁目录 (必须)
#   LOG_FILE   : 日志文件 (可选)


# ==================== 参数处理 ====================
if [ $# -lt 2 ]; then
    echo "Error: Missing required parameters"
    echo "Usage: $0 <APP_DIR> <PATCH_DIR> [LOG_FILE]"
    exit 1
fi

# 必需参数
APP_DIR="$1"      # 旧版本目录（将被升级的目录）
PATCH_DIR="$2"    # 补丁目录

if [ $# -eq 3 ]; then
    LOG_FILE="$3"
else
    LOG_FILE="/tmp/patch_log/patch_$(date +%Y%m%d%H%M%S).log"   # 默认日志路径
fi

mkdir -p "$(dirname "$LOG_FILE")"

# ==================== 目录校验 ====================
for dir in "$APP_DIR" "$PATCH_DIR"; do
    if [ ! -d "$dir" ]; then
        echo "Error: Directory does not exist $dir"
        exit 1
    fi
done

# ==================== 日志初始化 ====================
echo "====== Starting differential update $(date) ======"
echo "Configuration:"
echo "APP_DIR   : $APP_DIR"
echo "PATCH_DIR : $PATCH_DIR"
echo "LOG_FILE  : $LOG_FILE"

echo "===== Starting differential update ====="
exec > >(tee "$LOG_FILE") 2>&1
# exec > >(tee -a "$LOG_FILE" > /dev/null) 2>&1


# ==================== 符号链接处理函数 ====================
# 按依赖关系排序处理符号链接
process_symlinks_sorted() {
    local temp_file="/tmp/symlinks_sorted.$$"
    local processed_file="/tmp/symlinks_processed.$$"
    local remaining_file="/tmp/symlinks_remaining.$$"

    # 提取所有符号链接到临时文件
    jq -c '.symlinks[]' "$manifest" > "$temp_file"

    # 初始化处理状态
    touch "$processed_file"
    cp "$temp_file" "$remaining_file"

    local max_iterations=100
    local iteration=0

    while [ -s "$remaining_file" ] && [ $iteration -lt $max_iterations ]; do
        local progress_made=false
        local new_remaining="/tmp/symlinks_new_remaining.$$"
        touch "$new_remaining"

        # 遍历剩余的符号链接
        while read -r link; do
            if [ -z "$link" ]; then
                continue
            fi

            local path="$APP_DIR/$(echo "$link" | jq -r .path)"
            local target=$(echo "$link" | jq -r .target)
            local link_dir=$(dirname "$path")
            local can_create=true

            # 检查目标是否是相对路径且指向另一个符号链接
            case "$target" in
                /*)
                    # 绝对路径，可以直接创建
                    ;;
                *)
                    # 相对路径，检查目标是否存在或者是否已经在处理队列中
                    local target_path="$link_dir/$target"
                    if [ ! -e "$target_path" ]; then
                        # 检查目标是否在剩余的符号链接列表中
                        local target_relative=$(echo "$target_path" | sed "s|^$APP_DIR/||")
                        if grep -q "\"path\":\"$target_relative\"" "$remaining_file" 2>/dev/null; then
                            can_create=false
                        fi
                    fi
                    ;;
            esac

            if [ "$can_create" = true ]; then
                # 创建符号链接
                echo "Updating symlink: $path -> $target"
                ln -sfn "$target" "$path"

                # 验证符号链接
                if [ ! -e "$path" ]; then
                    case "$target" in
                        /*)
                            echo "Warning: Symlink target does not exist $target"
                            ;;
                        *)
                            if [ ! -e "$link_dir/$target" ]; then
                                echo "Warning: Symlink target does not exist $target (in $link_dir)"
                            fi
                            ;;
                    esac
                fi

                # 标记为已处理
                echo "$link" >> "$processed_file"
                progress_made=true
            else
                # 保留到下一轮处理
                echo "$link" >> "$new_remaining"
            fi
        done < "$remaining_file"

        # 更新剩余列表
        mv "$new_remaining" "$remaining_file"
        iteration=$((iteration + 1))

        # 如果没有进展，强制处理剩余的符号链接
        if [ "$progress_made" = false ] && [ -s "$remaining_file" ]; then
            echo "Warning: Detected symlink circular dependency, forcing remaining symlinks"
            while read -r link; do
                if [ -z "$link" ]; then
                    continue
                fi
                local path="$APP_DIR/$(echo "$link" | jq -r .path)"
                local target=$(echo "$link" | jq -r .target)
                echo "Updating symlink: $path -> $target"
                ln -sfn "$target" "$path"
            done < "$remaining_file"
            break
        fi
    done

    # 清理临时文件
    rm -f "$temp_file" "$processed_file" "$remaining_file" "/tmp/symlinks_new_remaining.$$"
}

# ==================== 依赖检查 ====================
check_dependencies() {
    local missing=0
    local deps=("bsdiff" "jq" "md5sum")

    for cmd in "${deps[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            echo "Error: Missing dependency - $cmd"
            missing=1
        fi
    done

    if [ $missing -ne 0 ]; then
        echo "Please install the above dependencies and try again"
        exit 1
    fi
}



# 校验清单（MD5和权限）
verify_manifest() {
    local manifest_type=$1
    local manifest_file="$PATCH_DIR/manifest.json"
    local base_dir="$2"
    local ret=0
    
    echo "Verifying $manifest_type manifest..."
    
    # 提取文件MD5和权限清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_md5=$(echo "$entry" | jq -r '.value.md5')
        expected_mode=$(echo "$entry" | jq -r '.value.mode')
        full_path="$base_dir/$path"
        
        # 检查文件是否存在
        if [ ! -f "$full_path" ]; then
            echo "Error: File does not exist $full_path"
            ret=1
            continue
        fi
        
        # 计算实际MD5
        actual_md5=$(md5sum "$full_path" | awk '{print $1}')
        
        if [ "$actual_md5" != "$expected_md5" ]; then
            echo "Error: MD5 mismatch $path (expected: $expected_md5, actual: $actual_md5)"
            ret=1
        fi
        
        # 校验权限
        actual_mode=$(stat -c "%a" "$full_path")
        if [ "$actual_mode" != "$expected_mode" ]; then
            echo "Error: Permission mismatch $path (expected: $expected_mode, actual: $actual_mode)"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.files | to_entries[]" "$manifest_file")
    
    # 提取目录权限清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_mode=$(echo "$entry" | jq -r '.value.mode')
        full_path="$base_dir/$path"
        
        # 检查目录是否存在
        if [ ! -d "$full_path" ]; then
            echo "Error: Directory does not exist $full_path"
            ret=1
            continue
        fi
        
        # 校验权限
        actual_mode=$(stat -c "%a" "$full_path")
        if [ "$actual_mode" != "$expected_mode" ]; then
            echo "Error: Directory permission mismatch $path (expected: $expected_mode, actual: $actual_mode)"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.dirs | to_entries[]" "$manifest_file")

    # 提取符号链接清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_target=$(echo "$entry" | jq -r '.value.target')
        full_path="$base_dir/$path"
        
        # 检查符号链接是否存在
        if [ ! -L "$full_path" ] && [ ! -f "$full_path" ] && [ ! -d "$full_path" ]; then
            echo "Error: Symlink does not exist $full_path"
            ret=1
            continue
        fi
        
        # 检查目标路径（如果是符号链接）
        if [ -L "$full_path" ]; then
            actual_target=$(readlink "$full_path")
            if [ "$actual_target" != "$expected_target" ]; then
                echo "Error: Symlink target mismatch $path (expected: $expected_target, actual: $actual_target)"
                ret=1
            fi
        else
            echo "Error: Path is not a symlink $path"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.links | to_entries[]" "$manifest_file")

    return $ret
}

# 执行补丁操作
apply_patch() {
    local manifest="$PATCH_DIR/manifest.json"
    if [[ ! -f "$manifest" ]]; then
        echo "Error: Cannot find manifest file $manifest"
        exit 1
    fi   

    # 处理目录操作（先创建目录结构）
    jq -c '.operations[]' "$manifest" | while read -r op; do
        case $(echo "$op" | jq -r .type) in
            "ADD_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .mode)
                echo "Creating directory: $path (mode: $mode)"
                mkdir -p "$path"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi
                ;;

            "DEL_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "Deleting directory: $path"
                rm -rf "$path" 2>/dev/null || true  # 忽略不存在的情况
                ;;

            "DEL_DIR_RECURSIVE")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "Recursively deleting directory: $path"
                rm -rf "$path" 2>/dev/null || true  # 忽略不存在的情况
                ;;

            "DEL_LINK")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "Deleting symlink: $path"
                if [[ -L "$path" ]]; then
                    rm -f "$path" 2>/dev/null || true
                elif [[ -e "$path" ]]; then
                    echo "Warning: $path is not a symlink, force deleting"
                    rm -rf "$path" 2>/dev/null || true
                fi
                ;;

            "CHMOD_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .new_mode)
                echo "Changing directory permission: $path → $mode"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi
                ;;
        esac
    done

    # 处理文件操作（后处理文件）
    jq -c '.operations[]' "$manifest" | while read -r op; do
        case $(echo "$op" | jq -r .type) in
            "ADD")
                rel_path=$(echo "$op" | jq -r .path)
                file_name=$(echo "$op" | jq -r .file)
                src_file="$PATCH_DIR/patches/$file_name"
                dest_file="$APP_DIR/$rel_path"
                mode=$(echo "$op" | jq -r .mode)
                
                echo "Adding file: $dest_file (source: $src_file)"
                mkdir -p "$(dirname "$dest_file")"
                cp -f "$src_file" "$dest_file"
                
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$dest_file"
                fi                    
                ;;
                
            "DEL")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "Deleting file: $path"
                rm -f "$path" 2>/dev/null || true
                ;;
                
            "PATCH")
                rel_path=$(echo "$op" | jq -r .path)
                old_file="$APP_DIR/$rel_path"
                patch_file="$PATCH_DIR/$(echo "$op" | jq -r .patch)"
                new_mode=$(echo "$op" | jq -r .new_mode)

                echo "Applying patch to: $old_file"
                if [[ ! -f "$old_file" ]]; then
                    echo "Error: Original file does not exist, cannot apply patch"
                    exit 1
                fi

                # 使用哈希扁平存储的补丁文件
                patch_hash=$(basename "$patch_file")
                actual_patch_file="$PATCH_DIR/patches/$patch_hash"
                
                if [ ! -f "$actual_patch_file" ]; then
                    echo "Error: Cannot find patch file $actual_patch_file"
                    exit 1
                fi

                "$BSPATCH_PATH" "$old_file" "${old_file}.tmp" "$actual_patch_file"
                mv -f "${old_file}.tmp" "$old_file"

                # 设置新文件的权限
                if [[ -n "$new_mode" && "$new_mode" != "null" ]]; then
                    chmod "$new_mode" "$old_file"
                    echo "Changing file permission: $old_file → $new_mode"
                fi
                ;;

            "CHMOD")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .new_mode)
                echo "Changing file permission: $path → $mode"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi                
                
                ;;    
        esac
    done

    # 处理符号链接 - 按依赖关系排序
    process_symlinks_sorted
}

# 主流程
main() {
    #检查系统是否安装了必要的依赖工具
    check_dependencies

    # 阶段0：升级前校验

    echo "===== Pre-upgrade verification ====="
    if verify_manifest "old_manifest" "$APP_DIR"; then
        echo "Pre-upgrade verification passed"
    else
        echo "Error: Pre-upgrade verification failed!"
        exit 1
    fi

    # Stage 1: Apply patches
    echo "Starting to apply patches..."
    apply_patch

    # Stage 2: Post-upgrade verification
    echo "===== Post-upgrade verification ====="
    if verify_manifest "new_manifest" "$APP_DIR"; then
        echo "Post-upgrade verification passed"
    else
        echo "Error: Post-upgrade verification failed!"
        exit 1
    fi

    echo "====== Upgrade successful ======"
    exit 0
}

main
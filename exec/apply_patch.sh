#!/bin/bash
set -eo pipefail

# ==================== 配置变量 ====================
BSPATCH_PATH="./bspatch"

# ==================== 使用方法 ====================
# 示例: ./apply_patch.sh APP_DIR PATCH_DIR
# 参数说明:
#   APP_DIR    : 旧版本目录 (必须)
#   PATCH_DIR  : 补丁目录 (必须)
#   LOG_FILE   : 日志文件 (可选)


# ==================== 参数处理 ====================
if [ $# -lt 2 ]; then
    echo "错误：缺少必要参数"
    echo "使用方法: $0 <APP_DIR> <PATCH_DIR> [LOG_FILE]"
    exit 1
fi

# 必需参数
APP_DIR="$1"      # 旧版本目录（将被升级的目录）
PATCH_DIR="$2"    # 补丁目录

if [ $# -eq 3 ]; then
    LOG_FILE="$3"
else
    LOG_FILE="/tmp/patch_log/patch_$(date +%Y%m%d%H%M%S).log"   # 默认日志路径
fi

mkdir -p "$(dirname "$LOG_FILE")"

# ==================== 目录校验 ====================
for dir in "$APP_DIR" "$PATCH_DIR"; do
    if [ ! -d "$dir" ]; then
        echo "错误：目录不存在 $dir"
        exit 1
    fi
done

# ==================== 日志初始化 ====================
echo "====== 开始执行差分升级 $(date) ======"
echo "参数配置:"
echo "APP_DIR   : $APP_DIR"
echo "PATCH_DIR : $PATCH_DIR"
echo "LOG_FILE  : $LOG_FILE"

echo "===== Starting differential update ====="
exec > >(tee "$LOG_FILE") 2>&1
# exec > >(tee -a "$LOG_FILE" > /dev/null) 2>&1


# ==================== 符号链接处理函数 ====================
# 按依赖关系排序处理符号链接
process_symlinks_sorted() {
    local temp_file="/tmp/symlinks_sorted.$$"
    local processed_file="/tmp/symlinks_processed.$$"
    local remaining_file="/tmp/symlinks_remaining.$$"

    # 提取所有符号链接到临时文件
    jq -c '.symlinks[]' "$manifest" > "$temp_file"

    # 初始化处理状态
    touch "$processed_file"
    cp "$temp_file" "$remaining_file"

    local max_iterations=100
    local iteration=0

    while [ -s "$remaining_file" ] && [ $iteration -lt $max_iterations ]; do
        local progress_made=false
        local new_remaining="/tmp/symlinks_new_remaining.$$"
        touch "$new_remaining"

        # 遍历剩余的符号链接
        while read -r link; do
            if [ -z "$link" ]; then
                continue
            fi

            local path="$APP_DIR/$(echo "$link" | jq -r .path)"
            local target=$(echo "$link" | jq -r .target)
            local link_dir=$(dirname "$path")
            local can_create=true

            # 检查目标是否是相对路径且指向另一个符号链接
            case "$target" in
                /*)
                    # 绝对路径，可以直接创建
                    ;;
                *)
                    # 相对路径，检查目标是否存在或者是否已经在处理队列中
                    local target_path="$link_dir/$target"
                    if [ ! -e "$target_path" ]; then
                        # 检查目标是否在剩余的符号链接列表中
                        local target_relative=$(echo "$target_path" | sed "s|^$APP_DIR/||")
                        if grep -q "\"path\":\"$target_relative\"" "$remaining_file" 2>/dev/null; then
                            can_create=false
                        fi
                    fi
                    ;;
            esac

            if [ "$can_create" = true ]; then
                # 创建符号链接
                echo "更新符号链接: $path -> $target"
                ln -sfn "$target" "$path"

                # 验证符号链接
                if [ ! -e "$path" ]; then
                    case "$target" in
                        /*)
                            echo "警告：符号链接目标不存在 $target"
                            ;;
                        *)
                            if [ ! -e "$link_dir/$target" ]; then
                                echo "警告：符号链接目标不存在 $target (在 $link_dir)"
                            fi
                            ;;
                    esac
                fi

                # 标记为已处理
                echo "$link" >> "$processed_file"
                progress_made=true
            else
                # 保留到下一轮处理
                echo "$link" >> "$new_remaining"
            fi
        done < "$remaining_file"

        # 更新剩余列表
        mv "$new_remaining" "$remaining_file"
        iteration=$((iteration + 1))

        # 如果没有进展，强制处理剩余的符号链接
        if [ "$progress_made" = false ] && [ -s "$remaining_file" ]; then
            echo "警告：检测到符号链接循环依赖，强制处理剩余符号链接"
            while read -r link; do
                if [ -z "$link" ]; then
                    continue
                fi
                local path="$APP_DIR/$(echo "$link" | jq -r .path)"
                local target=$(echo "$link" | jq -r .target)
                echo "更新符号链接: $path -> $target"
                ln -sfn "$target" "$path"
            done < "$remaining_file"
            break
        fi
    done

    # 清理临时文件
    rm -f "$temp_file" "$processed_file" "$remaining_file" "/tmp/symlinks_new_remaining.$$"
}

# ==================== 依赖检查 ====================
check_dependencies() {
    local missing=0
    local deps=("bsdiff" "jq" "md5sum")

    for cmd in "${deps[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            echo "错误：缺少依赖 - $cmd"
            missing=1
        fi
    done

    if [ $missing -ne 0 ]; then
        echo "请安装以上依赖后重试"
        exit 1
    fi
}



# 校验清单（MD5和权限）
verify_manifest() {
    local manifest_type=$1
    local manifest_file="$PATCH_DIR/manifest.json"
    local base_dir="$2"
    local ret=0
    
    echo "校验 $manifest_type 清单..."
    
    # 提取文件MD5和权限清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_md5=$(echo "$entry" | jq -r '.value.md5')
        expected_mode=$(echo "$entry" | jq -r '.value.mode')
        full_path="$base_dir/$path"
        
        # 检查文件是否存在
        if [ ! -f "$full_path" ]; then
            echo "错误：文件不存在 $full_path"
            ret=1
            continue
        fi
        
        # 计算实际MD5
        actual_md5=$(md5sum "$full_path" | awk '{print $1}')
        
        if [ "$actual_md5" != "$expected_md5" ]; then
            echo "错误：MD5不匹配 $path (期望: $expected_md5, 实际: $actual_md5)"
            ret=1
        fi
        
        # 校验权限
        actual_mode=$(stat -c "%a" "$full_path")
        if [ "$actual_mode" != "$expected_mode" ]; then
            echo "错误：权限不匹配 $path (期望: $expected_mode, 实际: $actual_mode)"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.files | to_entries[]" "$manifest_file")
    
    # 提取目录权限清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_mode=$(echo "$entry" | jq -r '.value.mode')
        full_path="$base_dir/$path"
        
        # 检查目录是否存在
        if [ ! -d "$full_path" ]; then
            echo "错误：目录不存在 $full_path"
            ret=1
            continue
        fi
        
        # 校验权限
        actual_mode=$(stat -c "%a" "$full_path")
        if [ "$actual_mode" != "$expected_mode" ]; then
            echo "错误：目录权限不匹配 $path (期望: $expected_mode, 实际: $actual_mode)"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.dirs | to_entries[]" "$manifest_file")

    # 提取符号链接清单
    while read -r entry; do
        path=$(echo "$entry" | jq -r '.key')
        expected_target=$(echo "$entry" | jq -r '.value.target')
        full_path="$base_dir/$path"
        
        # 检查符号链接是否存在
        if [ ! -L "$full_path" ] && [ ! -f "$full_path" ] && [ ! -d "$full_path" ]; then
            echo "错误：符号链接不存在 $full_path"
            ret=1
            continue
        fi
        
        # 检查目标路径（如果是符号链接）
        if [ -L "$full_path" ]; then
            actual_target=$(readlink "$full_path")
            if [ "$actual_target" != "$expected_target" ]; then
                echo "错误：符号链接目标不匹配 $path (期望: $expected_target, 实际: $actual_target)"
                ret=1
            fi
        else
            echo "错误：路径不是符号链接 $path"
            ret=1
        fi
    done < <(jq -c ".${manifest_type}.links | to_entries[]" "$manifest_file")

    return $ret
}

# 执行补丁操作
apply_patch() {
    local manifest="$PATCH_DIR/manifest.json"
    if [[ ! -f "$manifest" ]]; then
        echo "错误：找不到清单文件 $manifest"
        exit 1
    fi   

    # 处理目录操作（先创建目录结构）
    jq -c '.operations[]' "$manifest" | while read -r op; do
        case $(echo "$op" | jq -r .type) in
            "ADD_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .mode)
                echo "创建目录: $path (权限: $mode)"
                mkdir -p "$path"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi
                ;;

            "DEL_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "删除目录: $path"
                rm -rf "$path" 2>/dev/null || true  # 忽略不存在的情况
                ;;

            "DEL_DIR_RECURSIVE")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "递归删除目录: $path"
                rm -rf "$path" 2>/dev/null || true  # 忽略不存在的情况
                ;;

            "DEL_LINK")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "删除软链接: $path"
                if [[ -L "$path" ]]; then
                    rm -f "$path" 2>/dev/null || true
                elif [[ -e "$path" ]]; then
                    echo "警告: $path 不是软链接，强制删除"
                    rm -rf "$path" 2>/dev/null || true
                fi
                ;;

            "CHMOD_DIR")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .new_mode)
                echo "修改目录权限: $path → $mode"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi
                ;;
        esac
    done

    # 处理文件操作（后处理文件）
    jq -c '.operations[]' "$manifest" | while read -r op; do
        case $(echo "$op" | jq -r .type) in
            "ADD")
                rel_path=$(echo "$op" | jq -r .path)
                file_name=$(echo "$op" | jq -r .file)
                src_file="$PATCH_DIR/patches/$file_name"
                dest_file="$APP_DIR/$rel_path"
                mode=$(echo "$op" | jq -r .mode)
                
                echo "添加文件: $dest_file (源: $src_file)"
                mkdir -p "$(dirname "$dest_file")"
                cp -f "$src_file" "$dest_file"
                
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$dest_file"
                fi                    
                ;;
                
            "DEL")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                echo "删除文件: $path"
                rm -f "$path" 2>/dev/null || true
                ;;
                
            "PATCH")
                rel_path=$(echo "$op" | jq -r .path)
                old_file="$APP_DIR/$rel_path"
                patch_file="$PATCH_DIR/$(echo "$op" | jq -r .patch)"
                new_mode=$(echo "$op" | jq -r .new_mode)

                echo "应用补丁到: $old_file"
                if [[ ! -f "$old_file" ]]; then
                    echo "错误：原文件不存在，无法打补丁"
                    exit 1
                fi

                # 使用哈希扁平存储的补丁文件
                patch_hash=$(basename "$patch_file")
                actual_patch_file="$PATCH_DIR/patches/$patch_hash"
                
                if [ ! -f "$actual_patch_file" ]; then
                    echo "错误：找不到补丁文件 $actual_patch_file"
                    exit 1
                fi

                "$BSPATCH_PATH" "$old_file" "${old_file}.tmp" "$actual_patch_file"
                mv -f "${old_file}.tmp" "$old_file"

                # 设置新文件的权限
                if [[ -n "$new_mode" && "$new_mode" != "null" ]]; then
                    chmod "$new_mode" "$old_file"
                    echo "修改文件权限: $old_file → $new_mode"
                fi
                ;;

            "CHMOD")
                path="$APP_DIR/$(echo "$op" | jq -r .path)"
                mode=$(echo "$op" | jq -r .new_mode)
                echo "修改文件权限: $path → $mode"
                # 仅当 mode 存在时才修改权限
                if [[ -n "$mode" ]]; then
                    chmod "$mode" "$path"
                fi                
                
                ;;    
        esac
    done

    # 处理符号链接 - 按依赖关系排序
    process_symlinks_sorted
}

# 主流程
main() {
    #检查系统是否安装了必要的依赖工具
    check_dependencies

    # 阶段0：升级前校验

    echo "===== 升级前校验 ====="
    if verify_manifest "old_manifest" "$APP_DIR"; then
        echo "升级前校验通过"
    else
        echo "错误：升级前校验失败！"
        exit 1
    fi

    # 阶段1：应用补丁
    echo "开始应用补丁..."
    apply_patch

    # 阶段2：升级后校验
    echo "===== 升级后校验 ====="
    if verify_manifest "new_manifest" "$APP_DIR"; then
        echo "升级后校验通过"
    else
        echo "错误：升级后校验失败！"
        exit 1
    fi

    echo "====== 升级成功 ======"
    exit 0
}

main
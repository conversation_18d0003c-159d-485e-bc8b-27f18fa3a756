#!/usr/bin/env python3
import os
import json
import hashlib
import subprocess
import shutil
from collections import defaultdict

class DiffGenerator:
    def __init__(self, old_dir, new_dir, output_dir):
        self.old_dir = os.path.abspath(old_dir)
        self.new_dir = os.path.abspath(new_dir)
        self.output_dir = os.path.abspath(output_dir)
        self.patch_dir = os.path.join(output_dir, 'patches')

        # 清理旧补丁目录
        if os.path.exists(self.patch_dir):
            shutil.rmtree(self.patch_dir)
        os.makedirs(self.patch_dir, exist_ok=True)

        # 初始化manifest数据结构
        self.manifest = {
            'operations': [],   # 文件操作记录
            'symlinks': [],     # 软链接变更
            'old_manifest': {
                'files': {},
                'dirs': {},
                'links': {}
            },
            'new_manifest': {
                'files': {},
                'dirs': {},
                'links': {}
            },
            'metadata': defaultdict(dict)  # 元数据存储
        }

    def get_file_metadata(self, path):
        """获取文件完整元数据"""
        # 修复：添加符号链接类型检测
        stat = os.stat(path, follow_symlinks=False)
        return {
            'mode': stat.st_mode & 0o7777,  # 包含完整权限位
            'is_dir': os.path.isdir(path),  # 是否是目录
            'is_file': os.path.isfile(path), # 是否是文件
            'is_link': os.path.islink(path)  # 是否是符号链接
        }

    def compare_permissions(self, old_path, new_path):
        """对比权限变更并记录"""
        # 修复：添加符号链接特殊处理
        old_meta = self.get_file_metadata(old_path)
        new_meta = self.get_file_metadata(new_path)

        # 记录权限变化
        if old_meta['mode'] != new_meta['mode']:
            return {
                'old_mode': oct(old_meta['mode'])[-3:],
                'new_mode': oct(new_meta['mode'])[-3:]
            }
        return None
    
    # MD5计算函数
    def get_md5(self, file_path):
        """计算文件MD5"""
        # 修复：添加符号链接特殊处理
        if os.path.islink(file_path):
            # 符号链接直接返回其目标路径的哈希
            target = os.readlink(file_path)
            hash_md5 = hashlib.md5()
            hash_md5.update(target.encode('utf-8'))
            return hash_md5.hexdigest()
        
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):   # 分块读取大文件，防止内存占用过高
                hash_md5.update(chunk)
        return hash_md5.hexdigest() # 返回32位MD5值

    def generate_manifest(self, dir_path):
        """生成目录的完整清单（包含MD5和权限）"""
        manifest = {
            'files': {},
            'dirs': {},
            'links': {}  # 修复：添加符号链接清单
        }
        
        # 处理根目录
        root_rel_path = ""
        manifest['dirs'][root_rel_path] = {
            'mode': oct(os.stat(dir_path).st_mode & 0o7777)[-3:]
        }
        
        # 处理文件和目录
        for root, dirs, files in os.walk(dir_path):
            # 计算当前目录的相对路径
            rel_root = os.path.relpath(root, dir_path)
            if rel_root == ".":
                rel_root = ""
            
            # 处理目录（包括空目录）
            for dir_name in dirs:
                dir_path_full = os.path.join(root, dir_name)
                
                # 修复：处理符号链接目录
                if os.path.islink(dir_path_full):
                    # 符号链接目录记录到links
                    rel_path = os.path.join(rel_root, dir_name) if rel_root else dir_name
                    manifest['links'][rel_path] = {
                        'target': os.readlink(dir_path_full),
                        'mode': oct(os.lstat(dir_path_full).st_mode & 0o7777)[-3:]
                    }
                    continue
                
                # 计算相对路径
                rel_path = os.path.join(rel_root, dir_name) if rel_root else dir_name
                
                # 记录目录权限
                manifest['dirs'][rel_path] = {
                    'mode': oct(os.stat(dir_path_full).st_mode & 0o7777)[-3:]
                }
            
            # 处理文件
            for file_name in files:
                file_path_full = os.path.join(root, file_name)
                
                # 修复：处理符号链接文件
                if os.path.islink(file_path_full):
                    # 符号链接文件记录到links
                    rel_path = os.path.join(rel_root, file_name) if rel_root else file_name
                    manifest['links'][rel_path] = {
                        'target': os.readlink(file_path_full),
                        'mode': oct(os.lstat(file_path_full).st_mode & 0o7777)[-3:]
                    }
                    continue
                
                # 计算相对路径
                rel_path = os.path.join(rel_root, file_name) if rel_root else file_name
                
                # 记录文件MD5和权限
                manifest['files'][rel_path] = {
                    'md5': self.get_md5(file_path_full),
                    'mode': oct(os.stat(file_path_full).st_mode & 0o7777)[-3:]
                }
        
        return manifest

    def store_file_by_hash(self, file_path, move=False, suffix=".data"):
        """将文件存储为哈希文件名"""
        file_hash = self.get_md5(file_path)
        dest_file = os.path.join(self.patch_dir, f"{file_hash}{suffix}")
        
        # 如果文件不存在或哈希不同，复制文件
        #if not os.path.exists(dest_file) or self.get_md5(dest_file) != file_hash:
        if move:
            shutil.move(file_path, dest_file)
        else:
            shutil.copy2(file_path, dest_file)
        
        return os.path.basename(dest_file)

    def process_file_changes(self, rel_path):
        """处理单个文件变更（核心逻辑优化）"""
        old_file = os.path.join(self.old_dir, rel_path)
        new_file = os.path.join(self.new_dir, rel_path)
        
        # 修复：检测符号链接变化
        old_exists = os.path.exists(old_file)
        new_exists = os.path.exists(new_file)
        old_is_link = os.path.islink(old_file) if old_exists else False
        new_is_link = os.path.islink(new_file) if new_exists else False
        
        # 情况1：符号链接变普通文件
        if old_is_link and not new_is_link and new_exists:
            # 删除旧符号链接
            self.manifest['operations'].append({
                'type': 'DEL',
                'path': rel_path
            })
            # 添加新普通文件
            file_name = self.store_file_by_hash(new_file)
            meta = self.get_file_metadata(new_file)
            self.manifest['operations'].append({
                'type': 'ADD',
                'path': rel_path,
                'file': file_name,
                'mode': oct(meta['mode'])[-3:],
            })
            return
            
        # 情况2：普通文件变符号链接
        if not old_is_link and new_is_link and old_exists:
            # 删除旧普通文件
            self.manifest['operations'].append({
                'type': 'DEL',
                'path': rel_path
            })
            # 添加新符号链接
            target = os.readlink(new_file)
            self.manifest['symlinks'].append({
                'path': rel_path,
                'target': target
            })
            return
        
        # 情况3：符号链接变符号链接（目标变化）
        if old_is_link and new_is_link:
            old_target = os.readlink(old_file)
            new_target = os.readlink(new_file)
            if old_target != new_target:
                self.manifest['symlinks'].append({
                    'path': rel_path,
                    'target': new_target,
                    'old_target': old_target
                })
            return
        
        # 以下处理普通文件的变化（原有逻辑）
        # ========== 处理新增文件 ==========
        if not old_exists:
            # 存储为哈希文件名
            file_name = self.store_file_by_hash(new_file)
            
            meta = self.get_file_metadata(new_file)
            self.manifest['operations'].append({
                'type': 'ADD',
                'path': rel_path,
                'file': file_name,  # 存储哈希文件名
                'mode': oct(meta['mode'])[-3:],
            })
            return

        # ========== 处理删除文件 ==========
        if not new_exists:
            self.manifest['operations'].append({
                'type': 'DEL',
                'path': rel_path
            })
            return

        # ========== 检测内容变化 ==========
        old_md5 = self.get_md5(old_file)
        new_md5 = self.get_md5(new_file)
        content_changed = old_md5 != new_md5

        # ========== 检测权限变化 ==========
        perm_change = self.compare_permissions(old_file, new_file)

        # ========== 生成补丁文件 ==========
        if content_changed:
            # 生成临时补丁文件
            temp_patch_file = os.path.join(self.patch_dir, f"{rel_path.replace('/', '_')}.bsdiff")
            os.makedirs(os.path.dirname(temp_patch_file), exist_ok=True)

            if os.path.exists(temp_patch_file):
                os.remove(temp_patch_file)

            print(f"正在处理文件: {rel_path}")
            try:
                subprocess.run(
                    ['bsdiff', old_file, new_file, temp_patch_file],
                    check=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    timeout=60  # 60秒超时
                )
            except subprocess.TimeoutExpired:
                print(f"警告：文件 {rel_path} 处理超时，跳过差分，使用完整替换")
                # 超时时使用完整文件替换
                file_name = self.store_file_by_hash(new_file)
                self.manifest['operations'].append({
                    'type': 'ADD',
                    'path': rel_path,
                    'file': file_name,
                    'mode': oct(self.get_file_metadata(new_file)['mode'])[-3:]
                })
                # 先删除旧文件
                self.manifest['operations'].insert(-1, {
                    'type': 'DEL',
                    'path': rel_path
                })
                return
            except subprocess.CalledProcessError as e:
                print(f"警告：文件 {rel_path} bsdiff失败，使用完整替换: {e}")
                # bsdiff失败时使用完整文件替换
                file_name = self.store_file_by_hash(new_file)
                self.manifest['operations'].append({
                    'type': 'ADD',
                    'path': rel_path,
                    'file': file_name,
                    'mode': oct(self.get_file_metadata(new_file)['mode'])[-3:]
                })
                # 先删除旧文件
                self.manifest['operations'].insert(-1, {
                    'type': 'DEL',
                    'path': rel_path
                })
                return
            
            # 存储为哈希文件名
            patch_file_name = self.store_file_by_hash(temp_patch_file, True)
            
            # 记录操作时使用哈希文件名
            operation = {
                'type': 'PATCH',
                'path': rel_path,
                'patch': f"patches/{patch_file_name}",
                'new_mode': oct(self.get_file_metadata(new_file)['mode'])[-3:]
            }
            
            # 合并权限变更信息
            if perm_change:
                operation.update(perm_change)
                
            self.manifest['operations'].append(operation)
        elif perm_change:  # 仅权限变化
            operation = {
                'type': 'CHMOD',
                'path': rel_path,
            }
            operation.update(perm_change)
            self.manifest['operations'].append(operation)

    def process_symlinks(self):
        """处理符号链接差异"""
        # 修复：处理所有符号链接类型
        for root, dirs, files in os.walk(self.new_dir):
            for name in files + dirs:
                path = os.path.join(root, name)
                if os.path.islink(path):
                    rel_path = os.path.relpath(path, self.new_dir)
                    old_path = os.path.join(self.old_dir, rel_path)
                    
                    # 跳过已在process_file_changes中处理的符号链接
                    if os.path.exists(old_path) and os.path.islink(old_path):
                        continue
                        
                    # 处理新增的符号链接
                    if not os.path.exists(old_path):
                        new_target = os.readlink(path)
                        self.manifest['symlinks'].append({
                            'path': rel_path,
                            'target': new_target
                        })
                        
    def process_directory_changes(self, rel_path):
        """处理目录权限变更"""
        old_dir = os.path.join(self.old_dir, rel_path)
        new_dir = os.path.join(self.new_dir, rel_path)

        if os.path.exists(old_dir) and os.path.exists(new_dir):
            perm_change = self.compare_permissions(old_dir, new_dir)
            if perm_change:
                self.manifest['operations'].append({
                    'type': 'CHMOD_DIR',
                    'path': rel_path,
                    **perm_change
                })

    def process_symlink_dir_conversions(self):
        """处理软链接和目录之间的转换"""
        # 收集所有需要处理的路径
        all_paths = set()
        processed_paths = set()

        # 收集旧版本中的所有路径
        for root, dirs, files in os.walk(self.old_dir):
            for name in dirs + files:
                path = os.path.join(root, name)
                rel_path = os.path.relpath(path, self.old_dir)
                all_paths.add(rel_path)

        # 收集新版本中的所有路径
        for root, dirs, files in os.walk(self.new_dir):
            for name in dirs + files:
                path = os.path.join(root, name)
                rel_path = os.path.relpath(path, self.new_dir)
                all_paths.add(rel_path)

        # 处理每个路径的转换
        for rel_path in all_paths:
            old_path = os.path.join(self.old_dir, rel_path)
            new_path = os.path.join(self.new_dir, rel_path)

            old_exists = os.path.exists(old_path)
            new_exists = os.path.exists(new_path)

            if not old_exists or not new_exists:
                continue

            old_is_link = os.path.islink(old_path)
            new_is_link = os.path.islink(new_path)
            old_is_dir = os.path.isdir(old_path) and not old_is_link
            new_is_dir = os.path.isdir(new_path) and not new_is_link

            # 情况1：软链接变目录
            if old_is_link and new_is_dir:
                converted_paths = self.handle_symlink_to_dir(rel_path, old_path, new_path)
                processed_paths.update(converted_paths)
            # 情况2：目录变软链接
            elif old_is_dir and new_is_link:
                converted_paths = self.handle_dir_to_symlink(rel_path, old_path, new_path)
                processed_paths.update(converted_paths)

        return processed_paths

    def handle_symlink_to_dir(self, rel_path, old_path, new_path):
        """处理软链接变目录的情况"""
        processed_paths = set()

        # 删除旧软链接
        self.manifest['operations'].append({
            'type': 'DEL_LINK',
            'path': rel_path
        })
        processed_paths.add(rel_path)

        # 创建新目录
        meta = self.get_file_metadata(new_path)
        self.manifest['operations'].append({
            'type': 'ADD_DIR',
            'path': rel_path,
            'mode': oct(meta['mode'])[-3:]
        })

        # 递归添加目录内容
        content_paths = self.add_directory_contents_recursively(new_path, rel_path)
        processed_paths.update(content_paths)

        return processed_paths

    def handle_dir_to_symlink(self, rel_path, old_path, new_path):
        """处理目录变软链接的情况"""
        processed_paths = set()

        # 收集旧目录中的所有路径
        for root, dirs, files in os.walk(old_path):
            for name in dirs + files:
                path = os.path.join(root, name)
                sub_rel_path = os.path.relpath(path, self.old_dir)
                processed_paths.add(sub_rel_path)

        # 递归删除旧目录
        self.manifest['operations'].append({
            'type': 'DEL_DIR_RECURSIVE',
            'path': rel_path
        })
        processed_paths.add(rel_path)

        # 创建新软链接
        target = os.readlink(new_path)
        self.manifest['symlinks'].append({
            'path': rel_path,
            'target': target
        })

        return processed_paths

    def add_directory_contents_recursively(self, dir_path, base_rel_path):
        """递归添加目录内容"""
        processed_paths = set()

        for root, dirs, files in os.walk(dir_path):
            # 计算相对于基础路径的相对路径
            current_rel = os.path.relpath(root, dir_path)
            if current_rel == '.':
                current_rel = ""

            full_rel_path = os.path.join(base_rel_path, current_rel) if current_rel else base_rel_path

            # 添加子目录
            for dir_name in dirs:
                sub_dir_path = os.path.join(root, dir_name)
                sub_rel_path = os.path.join(full_rel_path, dir_name) if full_rel_path else dir_name
                processed_paths.add(sub_rel_path)

                if os.path.islink(sub_dir_path):
                    # 子目录是软链接
                    target = os.readlink(sub_dir_path)
                    self.manifest['symlinks'].append({
                        'path': sub_rel_path,
                        'target': target
                    })
                else:
                    # 子目录是普通目录
                    meta = self.get_file_metadata(sub_dir_path)
                    self.manifest['operations'].append({
                        'type': 'ADD_DIR',
                        'path': sub_rel_path,
                        'mode': oct(meta['mode'])[-3:]
                    })

            # 添加文件
            for file_name in files:
                file_path = os.path.join(root, file_name)
                file_rel_path = os.path.join(full_rel_path, file_name) if full_rel_path else file_name
                processed_paths.add(file_rel_path)

                if os.path.islink(file_path):
                    # 文件是软链接
                    target = os.readlink(file_path)
                    self.manifest['symlinks'].append({
                        'path': file_rel_path,
                        'target': target
                    })
                else:
                    # 文件是普通文件
                    file_hash = self.store_file_by_hash(file_path)
                    meta = self.get_file_metadata(file_path)
                    self.manifest['operations'].append({
                        'type': 'ADD',
                        'path': file_rel_path,
                        'file': file_hash,
                        'mode': oct(meta['mode'])[-3:]
                    })

        return processed_paths

    def generate_diff(self):
        """主生成函数"""
        # 生成新旧版本完整清单（包含MD5和权限）
        print("生成旧版本清单...")
        self.manifest['old_manifest'] = self.generate_manifest(self.old_dir)
        print("生成新版本清单...")
        self.manifest['new_manifest'] = self.generate_manifest(self.new_dir)
        print("开始生成差分包...")

        # 首先处理软链接变目录和目录变软链接的情况
        processed_paths = self.process_symlink_dir_conversions()

        # 遍历新目录结构
        for root, dirs, files in os.walk(self.new_dir):
            # 计算相对路径
            rel_path = os.path.relpath(root, self.new_dir)
            if rel_path == '.':
                rel_path = ""

            # 跳过已经在软链接转换中处理的路径
            if rel_path in processed_paths:
                continue

            # 处理新增目录
            old_dir_path = os.path.join(self.old_dir, rel_path)
            if not os.path.exists(old_dir_path):
                meta = self.get_file_metadata(root)
                self.manifest['operations'].append({
                    'type': 'ADD_DIR',
                    'path': rel_path,
                    'mode': oct(meta['mode'])[-3:]
                })
            elif os.path.islink(old_dir_path) and os.path.isdir(root):
                # 软链接变目录的情况已在process_symlink_dir_conversions中处理
                pass
            else:  # 检查目录权限变化
                self.process_directory_changes(rel_path)

            # 处理文件差异 - 修复：不再跳过符号链接
            for file in files:
                file_path_full = os.path.join(root, file)
                file_rel_path = os.path.join(rel_path, file) if rel_path else file

                # 跳过已经在软链接转换中处理的文件
                if file_rel_path in processed_paths:
                    continue

                self.process_file_changes(file_rel_path)

        # 处理旧目录多余文件/目录
        for root, dirs, files in os.walk(self.old_dir):
            # 计算相对路径
            rel_path = os.path.relpath(root, self.old_dir)
            if rel_path == '.':
                rel_path = ""
            
            # 检查目录是否在新目录中存在
            new_dir_path = os.path.join(self.new_dir, rel_path)
            if not os.path.exists(new_dir_path):
                self.manifest['operations'].append({
                    'type': 'DEL_DIR',
                    'path': rel_path
                })
                continue
            
            # 标记需要删除的文件 - 修复：不再跳过符号链接
            for file in files:
                # 文件相对路径
                file_rel_path = os.path.join(rel_path, file) if rel_path else file
                new_file = os.path.join(self.new_dir, file_rel_path)
                if not os.path.exists(new_file):
                    self.manifest['operations'].append({
                        'type': 'DEL',
                        'path': file_rel_path
                    })

        # 处理符号链接
        self.process_symlinks()

        if len(self.manifest['operations']) == 0 and len(self.manifest['symlinks']) == 0:
            print("没有发现差异，无需生成补丁！")
            exit(0)

        # 写入清单文件
        with open(os.path.join(self.output_dir, 'manifest.json'), 'w') as f:
            json.dump(self.manifest, f, indent=2)

if __name__ == "__main__":  # 主函数入口
    import argparse  # 导入argparse标准库模块，argparse模块用于解析命令行参数
    parser = argparse.ArgumentParser(description='生成差分升级包')  # 创建一个ArgumentParser对象，用于解析命令行参数
    # 从命令行接收三个必要的输入，分别是旧版本目录、新版本目录和输出目录的路径，以便后续生成差分升级包
    parser.add_argument('old_dir', help='旧版本目录路径')   
    parser.add_argument('new_dir', help='新版本目录路径')
    parser.add_argument('output_dir', help='补丁输出目录')
    args = parser.parse_args()  # 解析命令行参数，并将结果保存到args变量中
    
    generator = DiffGenerator(args.old_dir, args.new_dir, args.output_dir)  # 创建一个DiffGenerator对象，用于生成差分升级包
    generator.generate_diff()  # 调用DiffGenerator对象的generate_diff()方法，生成差分升级包
    print(f"差分包生成完成！输出目录：{args.output_dir}")
#!/bin/bash
set -eo pipefail

# ==================== 测试工具函数 ====================
cleanup() {
    #return
    rm -rf test_old test_new test_patch test_app
}

create_file() {
    local path="$1"
    local content="$2"
    local perm="${3:-644}"
    mkdir -p "$(dirname "$path")"
    echo -e "$content" > "$path"
    chmod "$perm" "$path"
}

create_dir() {
    mkdir -p "$1"
    chmod "${2:-755}" "$1"
}

create_symlink() {
    ln -sf "$2" "$1"
}

validate_upgrade() {
    # 深度验证目录结构和内容
    if ! diff -r --no-dereference "$1" "$2"; then
        echo "❌ 验证失败: 目录内容或结构不一致"
        return 1
    fi

    # 验证文件权限
    while IFS= read -r -d '' file; do
        file_rel="${file#$1/}"
        file2="$2/$file_rel"
        
        # 获取权限
        perms1=$(stat -c "%a" "$file")
        perms2=$(stat -c "%a" "$file2")
        
        if [ "$perms1" != "$perms2" ]; then
            echo "❌ 权限不同: $file_rel ($perms1 vs $perms2)"
            return 1
        fi
    done < <(find "$1" -type f -print0)

    # 验证符号链接
    while IFS= read -r -d '' link; do
        link_rel="${link#$1/}"
        link2="$2/$link_rel"
        
        target1=$(readlink "$link")
        target2=$(readlink "$link2")
        
        if [ "$target1" != "$target2" ]; then
            echo "❌ 符号链接目标不同: $link_rel ($target1 vs $target2)"
            return 1
        fi
    done < <(find "$1" -type l -print0)

    echo "✅ 验证成功: 目录完全一致"
    return 0
}

# ==================== 测试用例 ====================
test_case1_add_files() {
    echo "测试用例1: 添加新文件"
    # 创建旧版本
    create_file "test_old/file1.txt" "内容1" 644
    create_file "test_old/subdir/file2.txt" "内容2" 600
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 添加新文件
    create_file "test_new/newfile.txt" "新文件内容" 640
    create_file "test_new/subdir/newfile2.txt" "新文件2内容" 750
    
    run_patch_test
}

test_case2_delete_files() {
    echo "测试用例2: 删除文件"
    # 创建带有多文件的旧版本
    create_file "test_old/file1.txt" "保留文件" 644
    create_file "test_old/to_delete.txt" "将被删除" 600
    create_file "test_old/subdir/file.txt" "保留文件" 640
    create_file "test_old/subdir/to_delete.txt" "将被删除" 700
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 删除文件
    rm test_new/to_delete.txt
    rm test_new/subdir/to_delete.txt
    
    run_patch_test
}

test_case3_modify_files() {
    echo "测试用例3: 修改文件内容"
    # 创建旧版本文件
    create_file "test_old/file.txt" "原始内容" 644
    create_file "test_old/subdir/file.txt" "原始内容" 600
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 修改文件内容
    echo "修改后的内容" > test_new/file.txt
    echo "子目录修改内容" > test_new/subdir/file.txt
    
    run_patch_test
}

test_case4_change_permissions() {
    echo "测试用例4: 修改权限"
    # 创建原始文件
    create_file "test_old/file.txt" "内容不变" 644
    create_dir "test_old/dir" 755
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 修改权限
    chmod 600 test_new/file.txt
    chmod 700 test_new/dir
    
    run_patch_test
}

test_case5_add_directory() {
    echo "测试用例5: 添加目录"
    # 基本目录结构
    create_file "test_old/file.txt" "内容" 644
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 添加新目录
    create_dir "test_new/newdir" 750
    create_file "test_new/newdir/file.txt" "新目录文件" 640
    
    run_patch_test
}

test_case6_delete_directory() {
    echo "测试用例6: 删除目录"
    # 创建带子目录的结构
    create_dir "test_old/todelete" 755
    create_file "test_old/todelete/file.txt" "将被删除" 600
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 删除目录
    rm -rf test_new/todelete
    
    run_patch_test
}

test_case7_symlinks() {
    echo "测试用例7: 符号链接处理"
    # 旧版本
    create_file "test_old/target.txt" "链接目标" 644
    create_symlink "test_old/link.txt" "target.txt"
    
    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/
    
    # 更新符号链接
    create_file "test_new/new_target.txt" "新目标" 600
    rm test_new/link.txt
    create_symlink "test_new/link.txt" "new_target.txt"
    
    run_patch_test
}

test_case8_combined() {
    echo "测试用例8: 组合操作（增删改+权限+目录）"
    # 旧版本
    create_file "test_old/keep.txt" "保留文件" 644
    create_file "test_old/modify.txt" "原始内容" 600
    create_file "test_old/delete.txt" "将被删除" 640
    create_dir "test_old/keep_dir" 755
    create_dir "test_old/delete_dir" 700

    # 创建新版本（复制旧版本内容）
    mkdir test_new
    cp -r test_old/* test_new/

    # 删除
    rm test_new/delete.txt
    rm -rf test_new/delete_dir

    # 修改
    echo "修改后的内容" > test_new/modify.txt
    chmod 644 test_new/modify.txt

    # 添加
    create_file "test_new/newfile.txt" "新增文件" 750
    create_dir "test_new/newdir" 700
    create_file "test_new/newdir/file.txt" "新目录文件" 600

    run_patch_test
}

test_case9_symlink_to_dir() {
    echo "测试用例9: 软链接变目录"
    # 旧版本：创建指向外部目录的软链接
    create_dir "test_old/external_dir" 755
    create_file "test_old/external_dir/file1.txt" "外部文件1" 644
    create_file "test_old/external_dir/file2.txt" "外部文件2" 600
    create_symlink "test_old/link_dir" "external_dir"

    # 新版本：软链接变成真实目录
    mkdir test_new
    cp -r test_old/* test_new/
    rm test_new/link_dir  # 删除软链接
    create_dir "test_new/link_dir" 755  # 创建真实目录
    create_file "test_new/link_dir/new_file1.txt" "新文件1" 644
    create_file "test_new/link_dir/new_file2.txt" "新文件2" 600
    create_file "test_new/link_dir/subdir/nested.txt" "嵌套文件" 640

    run_patch_test
}

test_case10_dir_to_symlink() {
    echo "测试用例10: 目录变软链接"
    # 旧版本：真实目录
    create_dir "test_old/real_dir" 755
    create_file "test_old/real_dir/file1.txt" "目录文件1" 644
    create_file "test_old/real_dir/file2.txt" "目录文件2" 600
    create_file "test_old/real_dir/subdir/nested.txt" "嵌套文件" 640
    # 创建目标目录
    create_dir "test_old/target_dir" 755
    create_file "test_old/target_dir/target_file.txt" "目标文件" 644

    # 新版本：目录变成软链接
    mkdir test_new
    cp -r test_old/* test_new/
    rm -rf test_new/real_dir  # 删除真实目录
    create_symlink "test_new/real_dir" "target_dir"  # 创建软链接

    run_patch_test
}

test_case11_complex_symlinks() {
    echo "测试用例11: 复杂符号链接依赖关系"
    # 旧版本：简单符号链接
    create_file "test_old/target.txt" "目标文件" 644
    create_symlink "test_old/link1.txt" "target.txt"

    # 新版本：复杂的符号链接链
    mkdir test_new
    cp -r test_old/* test_new/

    # 创建符号链接链：link1 -> link2 -> link3 -> target.txt
    create_file "test_new/final_target.txt" "最终目标" 644
    create_symlink "test_new/link3.txt" "final_target.txt"
    create_symlink "test_new/link2.txt" "link3.txt"
    rm test_new/link1.txt
    create_symlink "test_new/link1.txt" "link2.txt"

    # 添加更多复杂的符号链接
    create_file "test_new/lib/libtest.so.1.2.3" "库文件" 755
    create_symlink "test_new/lib/libtest.so.1" "libtest.so.1.2.3"
    create_symlink "test_new/lib/libtest.so" "libtest.so.1"

    run_patch_test
}

test_case12_large_files() {
    echo "测试用例12: 大文件处理"
    # 创建大文件（模拟二进制文件）
    create_file "test_old/small.bin" "$(head -c 1024 /dev/zero | tr '\0' 'A')" 644
    create_file "test_old/medium.bin" "$(head -c 10240 /dev/zero | tr '\0' 'B')" 644

    # 新版本：修改大文件
    mkdir test_new
    cp -r test_old/* test_new/

    # 修改文件内容
    echo "$(head -c 1024 /dev/zero | tr '\0' 'C')" > test_new/small.bin
    echo "$(head -c 10240 /dev/zero | tr '\0' 'D')" > test_new/medium.bin

    # 添加新的大文件
    create_file "test_new/large.bin" "$(head -c 20480 /dev/zero | tr '\0' 'E')" 644

    run_patch_test
}

test_case13_rollback_test() {
    echo "测试用例13: 回滚功能测试"
    # 旧版本
    create_file "test_old/file1.txt" "原始内容1" 644
    create_file "test_old/file2.txt" "原始内容2" 600
    create_dir "test_old/dir1" 755
    create_file "test_old/dir1/nested.txt" "嵌套文件" 640
    create_symlink "test_old/link.txt" "file1.txt"

    # 新版本：做一些修改
    mkdir test_new
    cp -r test_old/* test_new/
    echo "修改后的内容1" > test_new/file1.txt
    echo "修改后的内容2" > test_new/file2.txt
    create_file "test_new/newfile.txt" "新文件" 644

    mkdir -p test_patch test_app
    cp -r test_old/* test_app/

    echo "生成补丁..."
    if ! ./generate_patch.py test_old test_new test_patch; then
        echo "❌ 补丁生成失败!"
        cleanup
        return 1
    fi

    # 故意破坏manifest来触发升级后校验失败
    echo "故意破坏manifest以触发回滚..."
    sed -i 's/"md5": "[^"]*"/"md5": "wronghash123456789abcdef"/g' test_patch/manifest.json | head -1

    echo "应用补丁（预期失败并回滚）..."
    if ./apply_patch.sh test_app test_patch; then
        echo "❌ 升级应该失败但却成功了!"
        cleanup
        return 1
    fi

    echo "验证回滚结果..."
    if validate_upgrade test_app test_old; then
        echo "✅ 回滚测试通过!"
    else
        echo "❌ 回滚测试失败!"
        cleanup
        return 1
    fi

    echo "========================================"
    cleanup
}

test_case14_edge_cases() {
    echo "测试用例14: 边界条件测试"
    # 测试特殊文件名和路径
    create_file "test_old/normal.txt" "普通文件" 644
    create_file "test_old/file with spaces.txt" "带空格的文件名" 644
    create_file "test_old/file-with-dashes.txt" "带横线的文件名" 644
    create_file "test_old/file_with_underscores.txt" "带下划线的文件名" 644
    create_file "test_old/UPPERCASE.TXT" "大写文件名" 644
    create_file "test_old/中文文件.txt" "中文文件名" 644

    # 创建深层目录结构
    create_dir "test_old/deep/nested/directory/structure" 755
    create_file "test_old/deep/nested/directory/structure/deep_file.txt" "深层文件" 644

    # 创建空文件和空目录
    touch "test_old/empty_file.txt"
    chmod 644 "test_old/empty_file.txt"
    create_dir "test_old/empty_dir" 755

    # 新版本：修改这些特殊文件
    mkdir test_new
    cp -r test_old/* test_new/

    echo "修改后的内容" > "test_new/file with spaces.txt"
    echo "新的深层内容" > "test_new/deep/nested/directory/structure/deep_file.txt"
    echo "不再是空文件" > "test_new/empty_file.txt"
    create_file "test_new/empty_dir/no_longer_empty.txt" "目录不再为空" 644

    run_patch_test
}

test_case15_permission_edge_cases() {
    echo "测试用例15: 权限边界测试"
    # 测试常见权限组合（避免777权限的umask问题）
    create_file "test_old/perm600.txt" "只读写权限文件" 600
    create_file "test_old/perm644.txt" "标准权限文件" 644
    create_file "test_old/perm755.txt" "可执行文件" 755

    create_dir "test_old/dir700" 700
    create_dir "test_old/dir755" 755

    # 新版本：先复制，然后修改权限
    mkdir test_new
    cp -rp test_old/* test_new/  # 使用-p保持权限

    # 修改权限
    chmod 644 "test_new/perm600.txt"
    chmod 600 "test_new/perm644.txt"
    chmod 700 "test_new/perm755.txt"

    chmod 755 "test_new/dir700"
    chmod 700 "test_new/dir755"

    run_patch_test
}

# ==================== 补丁测试核心 ====================
run_patch_test() {
    # 准备补丁和应用目录
    mkdir -p test_patch test_app
    cp -r test_old/* test_app/

    echo "生成补丁..."
    if ! ./generate_patch.py test_old test_new test_patch; then
        echo "❌ 补丁生成失败!"
        cleanup
        exit 1
    fi

    echo "应用补丁..."
    if ! ./apply_patch.sh test_app test_patch; then
        echo "❌ 补丁应用失败!"
        cleanup
        exit 1
    fi

    echo "验证升级结果..."
    if validate_upgrade test_app test_new; then
        echo "✅ 测试通过!"
    else
        echo "❌ 测试失败!"
        cleanup
        exit 1
    fi

    echo "========================================"
    cleanup
}

# ==================== 性能测试 ====================
run_performance_test() {
    echo "🚀 性能测试: 大规模文件处理"

    # 创建大量文件的测试环境
    echo "创建测试环境（1000个文件）..."
    for i in {1..1000}; do
        create_file "test_old/file_$i.txt" "内容 $i" 644
        if [ $((i % 10)) -eq 0 ]; then
            create_dir "test_old/dir_$i" 755
            create_file "test_old/dir_$i/nested_$i.txt" "嵌套内容 $i" 600
        fi
    done

    # 创建新版本
    mkdir test_new
    cp -r test_old/* test_new/

    # 修改部分文件
    for i in {1..100}; do
        echo "修改后的内容 $i" > "test_new/file_$i.txt"
    done

    # 删除部分文件
    for i in {901..1000}; do
        rm -f "test_new/file_$i.txt"
    done

    # 添加新文件
    for i in {1001..1100}; do
        create_file "test_new/new_file_$i.txt" "新内容 $i" 644
    done

    echo "开始性能测试..."
    local start_time=$(date +%s)

    run_patch_test

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    echo "✅ 性能测试完成，耗时: ${duration}秒"
    echo "========================================"
}

# ==================== 主执行流程 ====================
show_help() {
    echo "差分升级脚本测试工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -a, --all           运行所有测试用例（默认）"
    echo "  -b, --basic         只运行基础测试用例"
    echo "  -s, --symlink       只运行符号链接相关测试"
    echo "  -r, --rollback      只运行回滚测试"
    echo "  -p, --performance   只运行性能测试"
    echo "  -e, --edge          只运行边界条件测试"
    echo "  -v, --verbose       详细输出模式"
    echo ""
    echo "测试用例说明:"
    echo "  基础测试: 文件增删改、权限修改、目录操作"
    echo "  符号链接测试: 软链接变目录、目录变软链接、复杂依赖"
    echo "  回滚测试: 升级失败时的回滚功能"
    echo "  性能测试: 大规模文件处理性能"
    echo "  边界测试: 特殊文件名、权限边界条件"
}

run_basic_tests() {
    echo "🔧 运行基础功能测试..."
    test_case1_add_files
    test_case2_delete_files
    test_case3_modify_files
    test_case4_change_permissions
    test_case5_add_directory
    test_case6_delete_directory
    test_case8_combined
}

run_symlink_tests() {
    echo "🔗 运行符号链接测试..."
    test_case7_symlinks
    test_case9_symlink_to_dir
    test_case10_dir_to_symlink
    test_case11_complex_symlinks
}

run_rollback_tests() {
    echo "🔄 运行回滚功能测试..."
    test_case13_rollback_test
}

run_edge_tests() {
    echo "⚡ 运行边界条件测试..."
    test_case12_large_files
    test_case14_edge_cases
    test_case15_permission_edge_cases
}

run_all_tests() {
    echo "🚀 运行完整测试套件..."
    echo "========================================"

    run_basic_tests
    run_symlink_tests
    run_rollback_tests
    run_edge_tests

    echo ""
    echo "✅ 所有功能测试完成!"
    echo "========================================"
}

main() {
    # 确保脚本可执行
    chmod +x generate_patch.py apply_patch.sh 2>/dev/null

    # 解析命令行参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--basic)
            cleanup
            run_basic_tests
            ;;
        -s|--symlink)
            cleanup
            run_symlink_tests
            ;;
        -r|--rollback)
            cleanup
            run_rollback_tests
            ;;
        -p|--performance)
            cleanup
            run_performance_test
            ;;
        -e|--edge)
            cleanup
            run_edge_tests
            ;;
        -a|--all|"")
            cleanup
            run_all_tests
            echo ""
            echo "🎯 运行性能测试..."
            run_performance_test
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 -h 或 --help 查看帮助信息"
            exit 1
            ;;
    esac

    echo ""
    echo "🎉 所有测试执行完成!"
}

# 启动主函数
main "$@"
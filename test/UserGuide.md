# 差分升级测试工具用户指南

## 概述

差分升级测试工具是一套用于验证差分升级功能的自动化测试工具，包含两个核心脚本：
- **`start_test.sh`** - 功能测试工具
- **`start_bin_test.sh`** - 二进制仓库测试工具

这些工具提供全面的测试覆盖，确保差分升级系统在各种场景下都能正常工作。

## 工具特性

### 🧪 测试覆盖范围
- ✅ **基础功能**：文件增删改、权限修改、目录操作
- ✅ **符号链接**：软链接变目录、目录变软链接、复杂依赖关系
- ✅ **回滚机制**：升级失败时的自动回滚验证
- ✅ **边界条件**：特殊文件名、权限边界、大文件处理
- ✅ **性能测试**：大规模文件处理性能验证
- ✅ **二进制测试**：真实Git仓库的差分升级测试

### 🚀 高级特性
- **模块化测试**：可单独运行特定类型的测试
- **自动化验证**：完整的结果验证和报告
- **性能监控**：测试执行时间统计
- **错误诊断**：详细的错误信息和调试支持

## 安装和准备

### 文件权限
```bash
chmod +x start_test.sh
chmod +x start_bin_test.sh
chmod +x generate_patch.py
chmod +x apply_patch.sh
```

## 功能测试工具 (start_test.sh)

### 基本使用

#### 运行所有测试
```bash
./start_test.sh
# 或
./start_test.sh --all
```

#### 查看帮助信息
```bash
./start_test.sh --help
```

### 测试选项

#### 基础功能测试
```bash
./start_test.sh --basic
```
测试内容：
- 测试用例1：添加新文件
- 测试用例2：删除文件
- 测试用例3：修改文件内容
- 测试用例4：修改权限
- 测试用例5：添加目录
- 测试用例6：删除目录
- 测试用例8：组合操作

#### 符号链接测试
```bash
./start_test.sh --symlink
```
测试内容：
- 测试用例7：基础符号链接处理
- 测试用例9：软链接变目录
- 测试用例10：目录变软链接
- 测试用例11：复杂符号链接依赖关系

#### 回滚功能测试
```bash
./start_test.sh --rollback
```
测试内容：
- 测试用例13：升级失败时的自动回滚验证

#### 边界条件测试
```bash
./start_test.sh --edge
```
测试内容：
- 测试用例12：大文件处理
- 测试用例14：特殊文件名和路径
- 测试用例15：权限边界测试

#### 性能测试
```bash
./start_test.sh --performance
```
测试内容：
- 创建1000个文件的测试环境
- 文件增删改性能验证
- 执行时间统计

### 测试用例详解

#### 测试用例1：添加新文件
- 创建新文件和子目录中的文件
- 验证文件内容和权限正确性

#### 测试用例2：删除文件
- 删除指定文件
- 验证文件确实被删除

#### 测试用例3：修改文件内容
- 修改现有文件的内容
- 验证内容变更正确

#### 测试用例4：修改权限
- 修改文件和目录权限
- 验证权限变更正确

#### 测试用例5：添加目录
- 创建新目录和其中的文件
- 验证目录结构和权限

#### 测试用例6：删除目录
- 删除整个目录树
- 验证目录完全删除

#### 测试用例7：符号链接处理
- 创建和修改符号链接
- 验证链接目标正确

#### 测试用例8：组合操作
- 同时进行增删改操作
- 验证复杂场景下的正确性

#### 测试用例9：软链接变目录
- 将符号链接转换为真实目录
- 验证转换过程和结果

#### 测试用例10：目录变软链接
- 将真实目录转换为符号链接
- 验证转换过程和结果

#### 测试用例11：复杂符号链接依赖
- 创建符号链接链（link1 -> link2 -> link3 -> target）
- 验证依赖关系处理正确

#### 测试用例12：大文件处理
- 处理不同大小的二进制文件
- 验证大文件处理性能

#### 测试用例13：回滚功能
- 故意触发升级失败
- 验证自动回滚机制

#### 测试用例14：边界条件
- 测试特殊文件名（空格、中文、特殊字符）
- 测试深层目录结构
- 测试空文件和空目录

#### 测试用例15：权限边界
- 测试各种权限组合
- 验证权限处理的准确性

## 二进制测试工具 (start_bin_test.sh)

### 基本使用

#### 运行二进制测试
```bash
./start_bin_test.sh
```

### 测试配置

工具默认配置：
```bash
REPO_URL="********************:horizon/j3/prebuilt/app_bin.git"
BRANCHES=("D2J" "D2J-V14.2.6")
MAX_DEPTH=4
```

### 测试流程

1. **仓库克隆**：克隆指定的Git仓库
2. **分支切换**：在不同分支间切换
3. **差分生成**：生成真实的差分补丁
4. **补丁应用**：应用补丁并验证结果
5. **结果验证**：对比升级前后的完整性

### 自定义配置

#### 修改测试仓库
```bash
# 编辑脚本中的配置
REPO_URL="your_git_repo_url"
BRANCHES=("branch1" "branch2")
```

#### 调整测试深度
```bash
# 限制目录扫描深度以提高性能
MAX_DEPTH=3
```

## 测试结果解读

### 成功标识
```
✅ 测试通过!
✅ 验证成功: 目录完全一致
✅ 回滚测试通过!
```

### 失败标识
```
❌ 测试失败!
❌ 验证失败: 目录内容或结构不一致
❌ 权限不同: file.txt (644 vs 755)
```

### 性能报告
```
✅ 性能测试完成，耗时: 34秒
🚀 性能测试: 大规模文件处理
创建测试环境（1000个文件）...
```

## 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 确保脚本有执行权限
chmod +x start_test.sh start_bin_test.sh

# 确保差分升级工具有执行权限
chmod +x generate_patch.py apply_patch.sh
```

#### 2. 依赖缺失
```bash
# 检查Python版本
python3 --version

# 检查必要工具
which diff patch md5sum stat
```

#### 3. Git访问问题（二进制测试）
```bash
# 检查Git配置
git config --list

# 测试仓库访问
git ls-remote ********************:horizon/j3/prebuilt/app_bin.git
```

#### 4. 磁盘空间不足
```bash
# 检查磁盘空间
df -h

# 清理测试临时文件
rm -rf test_old test_new test_patch test_app
```

### 调试技巧

#### 启用详细输出
```bash
# 在脚本开头添加调试选项
set -x  # 显示执行的命令
```

#### 保留测试数据
```bash
# 注释掉cleanup函数调用，保留测试数据进行分析
# cleanup
```

#### 单步执行
```bash
# 手动执行测试步骤
./generate_patch.py test_old test_new test_patch
./apply_patch.sh test_app test_patch
```

## 扩展和定制

### 添加新测试用例
```bash
# 在start_test.sh中添加新函数
test_case_new() {
    echo "测试用例X: 新功能测试"
    # 测试逻辑
    run_patch_test
}

# 在主函数中调用
test_case_new
```

### 自定义验证逻辑
```bash
# 修改validate_upgrade函数
validate_upgrade() {
    # 自定义验证逻辑
    # 返回0表示成功，非0表示失败
}
```

## 测试报告示例

### 完整测试报告
```
🚀 运行完整测试套件...
========================================
🔧 运行基础功能测试...
测试用例1: 添加新文件
✅ 测试通过!
测试用例2: 删除文件
✅ 测试通过!
测试用例3: 修改文件内容
✅ 测试通过!
...

🔗 运行符号链接测试...
测试用例7: 符号链接处理
✅ 测试通过!
测试用例9: 软链接变目录
✅ 测试通过!
...

🔄 运行回滚功能测试...
测试用例13: 回滚功能测试
✅ 回滚测试通过!

⚡ 运行边界条件测试...
测试用例12: 大文件处理
✅ 测试通过!
...

🎯 运行性能测试...
🚀 性能测试: 大规模文件处理
创建测试环境（1000个文件）...
✅ 性能测试完成，耗时: 34秒

🎉 所有测试执行完成!
```

### 失败测试报告
```
❌ 测试失败!
错误信息: MD5不匹配 file.txt (期望: abc123, 实际: def456)
建议: 检查文件内容是否正确生成
```

#!/bin/bash
set -eo pipefail

REPO_URL="********************:horizon/j3/prebuilt/app_bin.git"
BRANCHES=("D2J" "D2J-V14.2.6")
MAX_DEPTH=4

# ==================== 测试工具函数 ====================
cleanup() {
    rm -rf test_old test_new test_patch test_app
}

create_file() {
    local path="$1"
    local content="$2"
    local perm="${3:-644}"
    mkdir -p "$(dirname "$path")"
    echo -e "$content" > "$path"
    chmod "$perm" "$path"
}

create_dir() {
    mkdir -p "$1"
    chmod "${2:-755}" "$1"
}

create_symlink() {
    ln -sf "$2" "$1"
}

validate_upgrade() {
    # 深度验证目录结构和内容
    if ! diff -r --no-dereference "$1" "$2"; then
        echo "❌ 验证失败: 目录内容或结构不一致"
        return 1
    fi

    # 验证文件权限
    while IFS= read -r -d '' file; do
        file_rel="${file#$1/}"
        file2="$2/$file_rel"
        
        # 获取权限
        perms1=$(stat -c "%a" "$file")
        perms2=$(stat -c "%a" "$file2")
        
        if [ "$perms1" != "$perms2" ]; then
            echo "❌ 权限不同: $file_rel ($perms1 vs $perms2)"
            return 1
        fi
    done < <(find "$1" -type f -print0)

    # 验证符号链接
    while IFS= read -r -d '' link; do
        link_rel="${link#$1/}"
        link2="$2/$link_rel"
        
        target1=$(readlink "$link")
        target2=$(readlink "$link2")
        
        if [ "$target1" != "$target2" ]; then
            echo "❌ 符号链接目标不同: $link_rel ($target1 vs $target2)"
            return 1
        fi
    done < <(find "$1" -type l -print0)

    echo "✅ 验证成功: 目录完全一致"
    return 0
}

# ==================== 补丁测试核心 ====================
run_patch_test() {
    # 准备补丁和应用目录
    mkdir -p test_patch test_app
    cp -r test_old/* test_app/
    
    echo "生成补丁..."
    ./generate_patch.py test_old test_new test_patch
    
    echo "应用补丁..."
    ./apply_patch.sh test_app test_patch
    
    echo "验证升级结果..."
    if validate_upgrade test_app test_new; then
        echo "✅ 测试通过!"
    else
        echo "❌ 测试失败!"
        exit 1
    fi
    
    echo "========================================"
    cleanup
}

# ==================== 仓库测试函数 ====================
test_repository() {
    local repo_url="$1"
    local branches=("${!2}")
    local max_depth="$3"
    local repo_dir="repo_temp"
    
    # 克隆或更新仓库
    if [ ! -d "$repo_dir" ]; then
        echo "克隆仓库: $repo_url"
        git clone "$repo_url" "$repo_dir"
    else
        echo "更新仓库: $repo_url"
        git -C "$repo_dir" fetch
    fi
    
    # 进入仓库目录
    pushd "$repo_dir" >/dev/null
    
    for branch in "${branches[@]}"; do
        echo "切换到分支: $branch"
        git checkout "$branch" --force
        git clean -fd
        git reset --hard origin/"$branch"
        
        # 获取当前HEAD提交
        current_head=$(git rev-parse HEAD)
        
        # 测试从旧版本升级到新版本
        for ((depth = 1; depth <= max_depth; depth++)); do
            # 尝试获取第depth个祖先提交
            if ! git rev-parse HEAD~$depth &>/dev/null; then
                echo "⚠️ 分支 $branch 没有深度 $depth 的祖先提交，跳过"
                break
            fi
            
            echo "测试分支 $branch 深度 $depth 升级"
            old_commit=$(git rev-parse HEAD~$depth)
            new_commit="$current_head"
            
            # 准备旧版本目录
            echo "准备旧版本: $old_commit"
            git worktree add ../test_old "$old_commit" --force
            rm -rf ../test_old/.git
            
            # 准备新版本目录
            echo "准备新版本: $new_commit"
            git worktree add ../test_new "$new_commit" --force
            rm -rf ../test_new/.git
            
            # 返回主目录运行测试
            popd >/dev/null
            run_patch_test
            pushd "$repo_dir" >/dev/null
            
            # 清理工作树
            git worktree prune
        done
    done
    
    popd >/dev/null
}

# ==================== 主执行流程 ====================
main() {
    # 确保脚本可执行
    chmod +x generate_patch.py apply_patch.sh 2>/dev/null
    
    # 运行仓库测试
    test_repository "$REPO_URL" BRANCHES[@] "$MAX_DEPTH"
    
    echo "所有测试用例执行完成!"
}

# 启动主函数
main "$@"